import BreadCrumbs from "@app/components/breadcrumbs/breadcrumbs";
import TileMap from "@app/components/tile-map/tile-map";
import Underlay from "@app/components/underlay/underlay";
import useIndexedDB from "@app/hooks/use-indexed-db";
import useMapboxRequest from "@app/hooks/use-mapbox-request";
import { coordinatesToBounds } from "@app/hooks/use-tile-map";
import {
  Stack,
  Typography,
  Grid,
  Popper,
  Paper,
  Divider,
  List,
  ListItem,
  Box,
  IconButton,
  ListItemButton,
} from "@mui/material";
import {
  BookType,
  Maybe as MaybeType,
  TrimmedProjectResponse,
  uuid,
  PortalProjectGroupedAllocationResponse,
} from "@rubiconcarbon/shared-types";
import { useMemo, useState, useEffect, MouseEvent, useRef } from "react";
import { CommonProductProps } from "../types/product";
import { Coordinate } from "@app/types/index-db";
import { ArrowForwardRounded, LocationOnRounded } from "@mui/icons-material";
import Link from "next/link";
import useIsInView from "@app/hooks/use-is-in-view";
import Maybe from "@app/components/maybe/maybe";
import { useMeasure, useOrientation } from "react-use";
import MapLocationMarker from "@app/components/map-location-marker/map-location-marker";
import classes from "../styles/product-header.module.scss";
import useIsMobile from "@app/providers/mobile";
import { useRouter } from "next/navigation";
import BuyButton from "@app/components/buy-button/buy-button";
import { currencyFormat } from "@rubiconcarbon/frontend-shared";
import ProductPriceDisplay from "@app/components/product-price-display/product-price-display";
import ProductTitle from "@app/components/product-title/product-title";
import { ProductIds } from "@app/constants/products";

type MarkerData = {
  productId: uuid;
  id: uuid;
  name: string;
  country?: {
    name: string;
    region: string;
  };
};

type MarkerPopperProps = {
  open: boolean;
  anchorEl: MaybeType<HTMLElement>;
  markerData: MarkerData[];
};

type LocationMarkerProps = {
  markerData?: MarkerData[];
  fontSize?: number;
};

const MarkerPopper = ({ open, anchorEl, markerData }: MarkerPopperProps): JSX.Element => {
  const location = markerData?.[0]?.country?.name;

  return (
    <Popper
      open={open}
      anchorEl={anchorEl}
      sx={{ display: "flex", flexDirection: "column", alignItems: "center", zIndex: 2 }}
      placement="bottom"
    >
      <Stack
        component={Paper}
        justifyContent="center"
        alignItems="center"
        sx={{
          padding: 1,
          borderRadius: 3,
          width: 250,
        }}
      >
        <Typography
          textAlign="center"
          sx={{ width: 200, display: "flex", justifyContent: "center", alignItems: "center", fontSize: 12, pb: 1 }}
        >
          <LocationOnRounded sx={{ color: "#F5D54A" }} fontSize="large" />
          {location}
        </Typography>
        <Divider flexItem />
        <List sx={{ maxHeight: "30vh", overflowY: "scroll" }}>
          {markerData.map(({ productId, id, name }) => (
            <ListItem key={id}>
              <ListItemButton sx={{ padding: 0 }}>
                <Link
                  href={`/products/${productId}/projects/${id}`}
                  style={{
                    display: "flex",
                    width: "100%",
                    justifyContent: "space-between",
                    alignItems: "center",
                    textDecoration: "none",
                    paddingLeft: "10px",
                  }}
                >
                  <Typography sx={{ fontSize: 12 }}>{name}</Typography>
                  <IconButton>
                    <ArrowForwardRounded fontSize="small" />
                  </IconButton>
                </Link>
              </ListItemButton>
            </ListItem>
          ))}
        </List>
      </Stack>
    </Popper>
  );
};

const LocationMarker = ({ markerData = [], fontSize }: LocationMarkerProps): JSX.Element => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const open = useMemo(() => Boolean(anchorEl), [anchorEl]);

  const handleMouseEnter = (event: MouseEvent<HTMLElement>): void => {
    event.preventDefault();
    setAnchorEl(event.currentTarget);
  };

  const handleMouseLeave = (event: MouseEvent<HTMLElement>): void => {
    event.preventDefault();
    setAnchorEl(null);
  };

  return (
    <MapLocationMarker
      fontSize={fontSize}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      popper={<MarkerPopper open={open} anchorEl={anchorEl} markerData={markerData} />}
    />
  );
};

const ProductHeader = ({ product }: CommonProductProps): JSX.Element => {
  const { push } = useRouter();
  const isSmallScreen = useIsMobile(600);
  const mapContainer = useRef<HTMLDivElement>(null);
  const [container, { width }] = useMeasure();
  const { angle, type } = useOrientation();
  const navIsInView = useIsInView(mapContainer, {
    threshold: angle === 90 && type.startsWith("landscape") ? 0.7 : 0.9,
  });

  const [indexToLocation, setIndexToLocation] = useState<Record<number, string>>({});

  const projects = useMemo(
    () =>
      ((product?.ownerAllocationsByProject as PortalProjectGroupedAllocationResponse[]) ?? [])
        .map((x) => x.project as TrimmedProjectResponse)
        .filter((x) => x !== undefined) ?? [],

    [product?.ownerAllocationsByProject],
  );

  const productId = useMemo(() => product?.id ?? "", [product]) as uuid;
  const projectLocations: string[] = useMemo(
    () => Array.from(new Set(projects?.map(({ country }) => country?.name ?? ""))),
    [projects],
  );

  const projectLocationsToMarkerData = useMemo(
    () =>
      projects?.reduce(
        (accum, { id, name, country }) => ({
          ...accum,
          [country?.name ?? ""]: [...(accum[country?.name ?? ""] ?? []), { productId, id, name, country }],
        }),
        {} as Record<string, MarkerData[]>,
      ),
    [projects, productId],
  );

  const {
    status,
    insert,
    signals: { projectCoordinates },
  } = useIndexedDB({
    name: "project",
    signals: {
      projectCoordinates: {
        collection: "coordinates",
        filters: projectLocations?.map((location) => ({ name: location })),
      },
    },
  });

  const [coordinatesToFetch, setCoordinatesToFetch] = useState<string[]>();
  const [markers, setMarkers] = useState<[number, number][]>([]);

  const { places } = useMapboxRequest({
    places: coordinatesToFetch?.map((location) => ({ query: location, types: ["country"], flatten: true })),
  });

  useEffect(() => {
    if (status === "open" && Array.isArray(projectCoordinates)) {
      if (Array.isArray(projectCoordinates) && projectCoordinates.length < (projectLocations?.length ?? 0)) {
        const projectsLocated = (projectCoordinates as Coordinate[]).map(({ name }) => name);
        const coordinatesToFetch = projectLocations?.filter((location) => !projectsLocated?.includes(location ?? ""));
        setCoordinatesToFetch(coordinatesToFetch);
      } else {
        const markers: [number, number][] = [];
        const indexToLocation: Record<number, string> = {};

        for (const [index, { name, longitude, latitude }] of Object.entries(projectCoordinates as Coordinate[])) {
          markers.push([longitude, latitude]);
          indexToLocation[Number(index)] = name;
        }

        setMarkers(markers);
        setIndexToLocation(indexToLocation);
      }
    }
  }, [projectCoordinates, projectLocations, status]);

  useEffect(() => {
    const { data, error } = places || {};

    if (!error && !!data) {
      const { flattened = {} } = data;

      insert<Coordinate>(
        Object.entries(flattened).map(([location, features]) => ({
          name: location,
          longitude: (features?.[0]?.coordinates as [number, number])[0],
          latitude: (features?.[0]?.coordinates as [number, number])[1],
        })),
        "coordinates",
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [places]);

  return (
    <Box className="map-container" ref={container}>
      <Underlay
        ref={mapContainer}
        Background={
          <Maybe condition={!isSmallScreen}>
            {() => (
              <TileMap
                container="product-details"
                seedWidth={300}
                seedHeight={400}
                loading={!!markers.length}
                projection="mercator"
                center={[0, 50]}
                markers={markers}
                bounds={coordinatesToBounds(markers)}
                boundedZoomFactor={1}
                initialBoundFocus={{ zoom: 0.005 }}
                renderMarker={(index?: number): JSX.Element => (
                  <LocationMarker
                    markerData={projectLocationsToMarkerData?.[indexToLocation[index ?? 0]]}
                    fontSize={30}
                  />
                )}
              />
            )}
          </Maybe>
        }
        bottom="unset"
      >
        <Stack
          sx={
            !navIsInView
              ? {
                  position: "fixed",
                  width,
                  top: 70,
                  right: 0,
                  backgroundColor: "#52B5CA",
                  zIndex: 10,
                }
              : { position: "unset", top: "unset", backgroundColor: "#00000080", zIndex: 0 }
          }
          className={classes.ProductHeader}
        >
          <Maybe condition={navIsInView}>
            <Stack direction="row" justifyContent="space-between" sx={{ overflowX: "auto", width: "100%" }}>
              <Typography className={classes.BreadCrumbs} variant="bodyCopyS" gap="0.25rem">
                <BreadCrumbs home="/overview" items={[{ name: "Portfolio Details" }]} />
              </Typography>
            </Stack>
          </Maybe>
          <Grid container justifyContent="space-between" alignItems="center" rowGap={1}>
            <Grid item xs={12} sm={5} lg={8} container justifyContent={{ xs: "center", sm: "flex-start" }}>
              <Stack direction="column">
                <ProductTitle
                  value={product?.name}
                  className={classes.ProductNameText}
                  component={Typography}
                  variant="displayM"
                  trademark={product?.id !== ProductIds.RUBICON_RATED_TONNE}
                />

                <Typography className={classes.ProductNameText}>{product?.description}</Typography>
              </Stack>
            </Grid>
            <Grid
              item
              xs={12}
              sm={7}
              lg={4}
              container
              justifyContent={{ xs: "center", sm: "flex-end" }}
              alignItems="center"
              gap={isSmallScreen ? 0 : 1}
            >
              <Stack direction="row" alignItems="center" gap={1}>
                <Maybe condition={!!product?.isEnabled && product?.type !== BookType.PORTFOLIO_CUSTOM}>
                  <BuyButton
                    className={classes.ProductPurchaseButton}
                    onClick={(): void => push(`/products/${product?.id}/purchase` as any)}
                  />
                  <ProductPriceDisplay
                    className={classes.ProductPriceText}
                    value={currencyFormat(product?.purchasePrice?.toString() ?? "")}
                  />
                </Maybe>
              </Stack>
            </Grid>
          </Grid>
        </Stack>
      </Underlay>
    </Box>
  );
};

export default ProductHeader;
