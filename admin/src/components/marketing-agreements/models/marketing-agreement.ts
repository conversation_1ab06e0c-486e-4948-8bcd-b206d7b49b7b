import {
  MarketingAgreementLineItemResponse,
  MarketingAgreementResponse,
  MarketingAgreementStatus,
  MarketingAgreementType,
  uuid,
} from "@rubiconcarbon/shared-types";
import { Transform, Type } from "class-transformer";
import { IsDate, IsNotEmpty, IsOptional, IsUrl, MinDate, ValidateIf, ValidateNested } from "class-validator";
import { PrimitiveTypeProject } from "@/models/primitive-type-project";
import { PrimitiveTypeVintage } from "@/models/primitive-type-vintage";
import { GenericTableRowModel } from "@/components/ui/generic-table/types/generic-table-row-model";
import { TrimmedOrganizationModel } from "@/models/organization";

const minDate = new Date();
minDate.setHours(0, 0, 0, 0);

export class MarketingAgreementLineItem
  implements Omit<MarketingAgreementLineItemResponse, "createdAt" | "updatedAt" | "projectVintage">
{
  constructor() {}

  _id: string = undefined; // for auto-generating render ids

  id: uuid;

  lineItemKey: string;

  uiKey: string = undefined;

  @ValidateIf((o: GenericTableRowModel<MarketingAgreementLineItem>) => o?.status === MarketingAgreementStatus.ISSUED)
  @IsNotEmpty({ message: "Required" })
  @Type(() => PrimitiveTypeVintage)
  projectVintage?: PrimitiveTypeVintage;

  @IsNotEmpty({ message: "Required" })
  amount: number;

  @ValidateIf((o: GenericTableRowModel<MarketingAgreementLineItem>) => o?.status === MarketingAgreementStatus.ISSUED)
  @IsNotEmpty({ message: "Required" })
  amountIssued?: number;

  status: MarketingAgreementStatus;

  @ValidateIf((o: GenericTableRowModel<MarketingAgreementLineItem>) => o?.creating)
  @IsDate({ message: "Invalid Date" })
  @IsNotEmpty({ message: "Required" })
  @Type(() => Date)
  originalDeliveryDate: Date;

  @ValidateIf((o: GenericTableRowModel<MarketingAgreementLineItem>) => !o?.creating)
  @MinDate(minDate, { message: "Date cannot be in the past" })
  @IsDate({ message: "Invalid Date" })
  @IsNotEmpty({ message: "Required" })
  @Type(() => Date)
  lastUpdatedDeliveryDate: Date;

  @ValidateIf((o: GenericTableRowModel<MarketingAgreementLineItem>) => !!o?.expirationDate)
  @MinDate(minDate, { message: "Date cannot be in the past" })
  @IsDate({ message: "Invalid Date" })
  @Type(() => Date)
  expirationDate: Date;

  docsCount: number;
}

export class MarketingAgreementModel
  implements
    Omit<
      MarketingAgreementResponse,
      "floorPrice" | "lineItems" | "createdAt" | "updatedAt" | "project" | "organization"
    >
{
  constructor() {}

  id: uuid = undefined;

  uiKey: string = undefined;

  @ValidateNested()
  @Type(() => PrimitiveTypeProject)
  project: PrimitiveTypeProject;

  @ValidateNested()
  @Type(() => TrimmedOrganizationModel)
  organization: TrimmedOrganizationModel;

  @IsOptional()
  type?: MarketingAgreementType;

  typeF?: string; // represents the text friendly type of the marketing agreement

  @IsNotEmpty({ message: "Required" })
  floorPrice: string = undefined; // decimal

  @Transform(({ value }) => (value === "" ? undefined : value))
  @IsOptional()
  @IsUrl({}, { message: "Invalid URL" })
  feeCalculatorUrl: string;

  @IsOptional()
  memo: string;

  @IsNotEmpty({ message: "Required" })
  status: MarketingAgreementStatus;

  lineItems: MarketingAgreementLineItem[];

  createdAt: string = undefined; // date

  updatedAt: string = undefined; // date

  docsCount: number;
}

export class MarketingAgreementFormModel {
  constructor() {}

  @ValidateNested()
  @Type(() => MarketingAgreementModel)
  amends: MarketingAgreementModel[];
}

export class MultiMarketingAgreementLineItemFormModel {
  constructor() {}

  @ValidateNested()
  @Type(() => MarketingAgreementLineItem)
  amends: MarketingAgreementLineItem[];
}
