import React, { useState, useContext, use<PERSON><PERSON>back, KeyboardEvent } from "react";
import { Box, TextField, Dialog, DialogContent, DialogTitle, FormLabel, Stack, Autocomplete } from "@mui/material";
import { BaseDialogProps } from "../../../models/dialogs";
import ActionButton from "../../ui/action-button/action-button-enhanced";
import { AxiosContext } from "@/providers/axios-provider";
import useSnackbarVariants from "@/utils/hooks/useEnqueueVariant";
import {
  AdminOrganizationQueryResponse,
  AdminOrganizationResponse,
  AdminModelPortfolioComponentResponse,
  uuid,
} from "@rubiconcarbon/shared-types";
import { classValidatorResolver } from "@hookform/resolvers/class-validator";
import { Controller, useForm } from "react-hook-form";
import { PortfolioModel } from "./portfolio-model";
import { useRequest } from "@rubiconcarbon/frontend-shared";
import useAutoCompleteOptions, { UseAutoCompleteOptionsReturnEntry } from "@/utils/hooks/useAutoCompleteOptions";
import { SERVER_PAGINATION_LIMIT } from "@/constants/constants";

interface NewPortfolioModalProps extends BaseDialogProps {
  onSave: (id: uuid) => void;
}

const PortfolioResolver = classValidatorResolver(PortfolioModel);

export default function NewPortfolioModal({ isOpen, onClose, onSave }: NewPortfolioModalProps): JSX.Element | null {
  const { data: customers } = useRequest<AdminOrganizationQueryResponse>({
    url: "admin/organizations",
    queryParams: {
      limit: SERVER_PAGINATION_LIMIT,
    },
  });

  const customerOptions = useAutoCompleteOptions({
    data: customers?.data || [],
    keys: ["id", "name"],
    label: (entry: AdminOrganizationResponse) => entry?.name,
    value: (entry: AdminOrganizationResponse) => entry?.id,
    postTransform: (data: UseAutoCompleteOptionsReturnEntry[]) =>
      data?.sort((a, b) => a?.label?.localeCompare(b?.label)),
  });

  const {
    handleSubmit,
    formState: { errors },
    control,
    reset,
    watch,
  } = useForm<PortfolioModel>({
    resolver: PortfolioResolver,
    mode: "onSubmit",
    defaultValues: new PortfolioModel(),
  });

  const portfolioNameValue = watch("portfolioName");
  const organizationIdValue = watch("organization");

  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const { api } = useContext(AxiosContext);
  const [submissionInFlight, setSubmissionInFlight] = useState<boolean>(false);

  const onSubmitHandler = useCallback(async () => {
    const payload = {
      name: portfolioNameValue,
      organizationId: organizationIdValue,
    };

    try {
      setSubmissionInFlight(true);
      const newPortfolio = await api.post<AdminModelPortfolioComponentResponse>(`admin/model-portfolios`, payload);
      enqueueSuccess("Successfully created portfolio");
      onSave(newPortfolio.data.id);
    } catch (error: any) {
      if (!!error?.response?.data?.message) {
        enqueueError(
          Array.isArray(error.response.data.message)
            ? error.response.data.message.join(", ")
            : error.response.data.message,
        );
      } else enqueueError("Unable to create portfolio");
    } finally {
      setSubmissionInFlight(false);
    }
  }, [portfolioNameValue, organizationIdValue, api, enqueueError, enqueueSuccess, onSave]);

  const onCloseHandler = (): void => {
    reset({
      portfolioName: "",
    });
    onClose();
  };

  const handleEnter = useCallback(
    async (e: KeyboardEvent): Promise<void> => {
      if (e.key == "Enter") await onSubmitHandler();
    },
    [onSubmitHandler],
  );

  return (
    <Dialog open={isOpen} onClose={onCloseHandler}>
      <DialogTitle
        sx={{
          color: "#383838",
          fontWeight: 600,
          backgroundColor: "#FDFFFC",
          width: "501px",
        }}
      >
        Create Portfolio
      </DialogTitle>
      <DialogContent sx={{ height: "380px", overflowY: "hidden" }}>
        <Box sx={{ marginTop: -4 }}>
          <form id="create-portfolio-form" onSubmit={handleSubmit(onSubmitHandler)}>
            <FormLabel component="legend" sx={{ marginTop: 1.5, marginBottom: 2.5, lineHeight: "150%" }}>
              To get started, enter a name for your Sandbox portfolio.
            </FormLabel>
            <Stack direction="column" gap={3}>
              <fieldset style={{ display: "contents" }}>
                <Controller
                  name="portfolioName"
                  control={control}
                  render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
                    <TextField
                      label="Portfolio Name"
                      value={value ?? ""}
                      InputProps={{ ref }}
                      disabled={submissionInFlight}
                      required
                      error={!!errors.portfolioName}
                      helperText={errors.portfolioName?.message}
                      {...otherProps}
                      onKeyDown={handleEnter}
                      inputProps={{ maxLength: 256 }}
                      sx={{ width: "100%" }}
                    />
                  )}
                />
              </fieldset>
              <fieldset style={{ display: "contents" }}>
                <Controller
                  name="organization"
                  control={control}
                  render={({ field: { ref, value, onChange, ...otherProps } }): JSX.Element => (
                    <Autocomplete
                      options={customerOptions}
                      value={customerOptions.find(({ value: v }) => v === value) || null}
                      loading
                      disablePortal
                      ListboxProps={{ style: { maxHeight: "150px" } }}
                      disabled={submissionInFlight}
                      id="customer"
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Select Organization"
                          inputRef={ref}
                          required
                          error={!!errors.organization}
                          helperText={errors.organization?.message}
                          {...otherProps}
                          fullWidth
                        />
                      )}
                      onChange={(_, selection) => onChange(selection?.value)}
                    />
                  )}
                />
              </fieldset>
            </Stack>
            <Box
              mt={11}
              sx={{
                borderTop: "1px solid lightGray",
                marginLeft: "-24px",
                marginRight: "-23px",
              }}
            ></Box>
            <Stack direction="row" mt={3} gap={2} sx={{ float: "right" }}>
              <ActionButton
                onClickHandler={onCloseHandler}
                style={{
                  borderColor: "rgba(0, 0, 0, 0.23)",
                  color: "rgba(0, 0, 0, 1)",
                  backgroundColor: "rgba(255, 255, 255, 1)",
                  fontWeight: 600,
                  "&:hover": {
                    backgroundColor: "rgba(255, 255, 255, 1)",
                    boxShadow: "rgb(0 0 0 / 10%) 0px 4px 4px",
                  },
                }}
              >
                Cancel
              </ActionButton>
              <ActionButton style={{ width: "100px" }} type="submit">
                Create
              </ActionButton>
            </Stack>
          </form>
        </Box>
      </DialogContent>
    </Dialog>
  );
}
