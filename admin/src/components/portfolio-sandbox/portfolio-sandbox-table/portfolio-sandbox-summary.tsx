import React, { useState, useEffect, useContext, useCallback } from "react";
import {
  Typography,
  Box,
  Paper,
  TableContainer,
  Table,
  TableRow,
  TableCell,
  TableHead,
  TableBody,
  LinearProgress,
} from "@mui/material";
import { PortfolioComponentResponse } from "./portfolio-sandbox-model";
import { Maybe, numberFormat } from "@rubiconcarbon/frontend-shared";
import { AdminPricingEstimateRequest, AdminPricingEstimateResponse, uuid } from "@rubiconcarbon/shared-types";
import { AxiosContext } from "@/providers/axios-provider";
import Decimal from "decimal.js";
import { MISSING_DATA } from "@/constants/constants";
import currencyFormat from "@/utils/formatters/currencyFormat";
import COLORS from "../../ui/theme/colors";
import { sum } from "lodash";

const separator = {
  width: "5px",
  padding: "0px",
  margin: "0px",
  borderLeft: "solid",
  borderWidth: "2px",
  borderLeftColor: "rgba(0, 0, 0, 0.12)",
  borderBottom: "none",
};

const cellBorder = {
  borderLeft: "solid",
  borderLeftWidth: "2px",
  borderLeftColor: "rgba(0, 0, 0, 0.12)",
};

interface AlgoPrice {
  unitPrice?: Decimal;
  unitCost?: Decimal;
  total?: Decimal;
  quantity?: number;
  mtm?: Decimal;
}

const buildAlgoPayload = (
  portfolioComponents: PortfolioComponentResponse[],
  includeBuffer: boolean,
): AdminPricingEstimateRequest[] => {
  const pricingEstimateRequest: AdminPricingEstimateRequest[] = [];
  const existingVintagesMap = new Map<uuid, number>();

  const pManagerEstimate = portfolioComponents.findIndex((c) => c.portfolioManagerEstimate === null);
  if (pManagerEstimate > 0) return null;

  portfolioComponents.forEach((c) => {
    if (!c.projectVintage?.id) {
      pricingEstimateRequest.push({
        quantity: c.amountAllocated,
        cost: c?.costBasis,
        price: c?.portfolioManagerEstimate,
        buffer: includeBuffer ? Number(c?.bufferPercentage) : 0,
        bufferCategoryId: c.bufferCategory?.id,
      });
    } else {
      if (existingVintagesMap.has(c.projectVintage.id)) {
        existingVintagesMap.set(c.projectVintage.id, existingVintagesMap.get(c.projectVintage.id) + c.amountAllocated);
      } else {
        existingVintagesMap.set(c.projectVintage.id, c.amountAllocated);
      }
    }
  });

  for (const [key, value] of existingVintagesMap) {
    pricingEstimateRequest.push({
      vintageId: key,
      quantity: value,
      buffer: includeBuffer ? undefined : 0,
    });
  }
  return pricingEstimateRequest;
};

export default function PortfolioSummary(props: { availableComponents: PortfolioComponentResponse[] }): JSX.Element {
  const { availableComponents } = props;
  const { api } = useContext(AxiosContext);
  const [portfolioQuantity, setPortfolioQuantity] = useState<number | undefined>(undefined);
  const [algorithmPrice, setAlgorithmPrice] = useState<AlgoPrice | undefined>(undefined);
  const [algorithmPriceWithRisk, setAlgorithmPriceWithRisk] = useState<AlgoPrice | undefined>(undefined);
  const [isMissingEstimate, setIsMissingEstimate] = useState<boolean>(false);

  const clearAllFields = (): void => {
    setPortfolioQuantity(undefined);
    setAlgorithmPrice(undefined);
    setAlgorithmPriceWithRisk(undefined);
  };

  const getAlgorithmPrice = useCallback(
    (totalQuantity: number, payload: AdminPricingEstimateRequest[], withRisk: boolean) => {
      const setFn = withRisk ? setAlgorithmPriceWithRisk : setAlgorithmPrice;
      if (payload?.length > 0) {
        api
          .post<AdminPricingEstimateResponse>("admin/pricing/estimate", payload)
          .then((data) => {
            if (!!data?.data?.priceEstimate && !!data?.data?.unitPrice) {
              const unitPrice =
                totalQuantity && !!data.data.priceEstimate
                  ? new Decimal(data.data.priceEstimate).dividedBy(totalQuantity)
                  : null;
              const unitCost =
                totalQuantity && !!data.data.priceEstimate
                  ? new Decimal(data.data.costEstimate).dividedBy(totalQuantity)
                  : null;
              const total = data.data.priceEstimate;
              const mtm = data.data.unitPrice;
              setFn({ unitPrice, unitCost, total, quantity: totalQuantity, mtm });
            } else {
              setFn(null);
            }
          })
          .catch((e) => {
            console.error(e);
          });
      }
    },
    [api],
  );

  useEffect(() => {
    if (!!availableComponents && availableComponents.length > 0) {
      clearAllFields();

      const portfolioComponents = availableComponents.filter((c) => !c.isBufferComponent);
      const totalPortfolioVolume = sum(portfolioComponents.map((x) => x.amountAllocated));
      setPortfolioQuantity(totalPortfolioVolume);

      if (!!portfolioComponents && portfolioComponents.length > 0) {
        // without risk
        let payload = buildAlgoPayload(portfolioComponents, false);
        !!payload ? setIsMissingEstimate(false) : setIsMissingEstimate(true);
        getAlgorithmPrice(totalPortfolioVolume, payload, false);

        // with risk
        payload = buildAlgoPayload(portfolioComponents, true);
        getAlgorithmPrice(totalPortfolioVolume, payload, true);
      } else {
        setAlgorithmPrice({});
        setAlgorithmPriceWithRisk({});
      }
    } else {
      setAlgorithmPrice({});
      setAlgorithmPriceWithRisk({});
      setPortfolioQuantity(0);
    }
  }, [availableComponents, getAlgorithmPrice]);

  return (
    <Box mt={0} mb={10}>
      <Box sx={{ display: "flex", justifyContent: "space-between" }}>
        <Typography variant="body1" component="h5" fontWeight="400" fontSize="24px">
          Portfolio Summary
        </Typography>
      </Box>
      <Paper sx={{ width: "100%", overflow: "hidden", marginTop: "20px" }}>
        <TableContainer>
          <Table aria-label="sandbox table" stickyHeader sx={{ overflowX: "auto" }}>
            <TableHead sx={{ backgroundColor: COLORS.tableHeader }}>
              <TableRow>
                <TableCell colSpan={2} sx={{ backgroundColor: "rgb(240, 240, 240)" }}>
                  <Typography variant="body1" component="h5" fontWeight="400" fontSize="18px">
                    Weighted Average MTM
                  </Typography>
                </TableCell>
                <TableCell
                  colSpan={3}
                  sx={{
                    ...cellBorder,
                    backgroundColor: "rgb(240, 240, 240)",
                  }}
                >
                  <Typography variant="body1" component="h5" fontWeight="400" fontSize="18px">
                    Algorithmic Price
                  </Typography>
                </TableCell>
                <TableCell
                  colSpan={3}
                  sx={{
                    ...cellBorder,
                    backgroundColor: "rgb(240, 240, 240)",
                  }}
                >
                  <Typography variant="body1" component="h5" fontWeight="400" fontSize="18px">
                    Risk-Adjusted Algorithmic Price
                  </Typography>
                </TableCell>
                <TableCell sx={separator}></TableCell>
                <TableCell colSpan={2} sx={{ ...cellBorder, backgroundColor: "#EBF8FF" }}>
                  <Typography variant="body1" component="h5" fontWeight="400" fontSize="18px">
                    Weighted Average Cost Basis
                  </Typography>
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell>
                  <Typography variant="body2" component="h6" fontWeight="600">
                    MTM
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" component="h6" fontWeight="600">
                    Risk Adjusted MTM
                  </Typography>
                </TableCell>

                <TableCell sx={{ ...cellBorder, borderLeftColor: "rgba(0, 0, 0, 0.12)" }}>
                  <Typography variant="body2" component="h6" fontWeight="600">
                    Price
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" component="h6" fontWeight="600">
                    Quantity
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" component="h6" fontWeight="600">
                    $ Total
                  </Typography>
                </TableCell>
                <TableCell sx={{ ...cellBorder, borderLeftColor: "rgba(0, 0, 0, 0.12)" }}>
                  <Typography variant="body2" component="h6" fontWeight="600">
                    Price
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" component="h6" fontWeight="600">
                    Quantity
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" component="h6" fontWeight="600">
                    $ Total
                  </Typography>
                </TableCell>
                <TableCell sx={separator}></TableCell>
                <TableCell sx={{ ...cellBorder, borderLeftColor: "rgba(0, 0, 0, 0.12)" }}>
                  <Typography variant="body2" component="h6" fontWeight="600">
                    Cost Basis
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" component="h6" fontWeight="600">
                    Risk Adjusted Cost Basis
                  </Typography>
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              <TableRow>
                <TableCell>
                  <Maybe condition={algorithmPrice === undefined && !isMissingEstimate}>
                    <LinearProgress sx={{ maxWidth: 50 }} />
                  </Maybe>
                  <Maybe condition={algorithmPrice !== undefined || isMissingEstimate}>
                    <Typography variant="body2" component="p" fontWeight="400">
                      {!!algorithmPrice?.mtm ? currencyFormat(+algorithmPrice.mtm) : MISSING_DATA}
                    </Typography>
                  </Maybe>
                </TableCell>
                <TableCell>
                  <Maybe condition={algorithmPriceWithRisk === undefined && !isMissingEstimate}>
                    <LinearProgress sx={{ maxWidth: 50 }} />
                  </Maybe>
                  <Maybe condition={algorithmPriceWithRisk !== undefined || isMissingEstimate}>
                    <Typography variant="body2" component="p" fontWeight="400">
                      {!!algorithmPriceWithRisk?.mtm ? currencyFormat(+algorithmPriceWithRisk.mtm) : MISSING_DATA}
                    </Typography>
                  </Maybe>
                </TableCell>
                <TableCell sx={{ ...cellBorder, borderLeftColor: "rgba(0, 0, 0, 0.12)" }}>
                  <Maybe condition={algorithmPrice === undefined && !isMissingEstimate}>
                    <LinearProgress sx={{ maxWidth: 50 }} />
                  </Maybe>
                  <Maybe condition={algorithmPrice !== undefined || isMissingEstimate}>
                    <Typography variant="body2" component="p" fontWeight="400">
                      {!!algorithmPrice?.unitPrice ? currencyFormat(+algorithmPrice.unitPrice) : MISSING_DATA}
                    </Typography>
                  </Maybe>
                </TableCell>
                <TableCell>
                  <Maybe condition={portfolioQuantity === undefined}>
                    <LinearProgress sx={{ maxWidth: 50 }} />
                  </Maybe>
                  <Maybe condition={portfolioQuantity !== undefined}>
                    <Typography variant="body2" component="p" fontWeight="400">
                      {numberFormat(portfolioQuantity)}
                    </Typography>
                  </Maybe>
                </TableCell>
                <TableCell>
                  <Maybe condition={algorithmPrice === undefined && !isMissingEstimate}>
                    <LinearProgress sx={{ maxWidth: 50 }} />
                  </Maybe>
                  <Maybe condition={algorithmPrice !== undefined || isMissingEstimate}>
                    <Typography variant="body2" component="p" fontWeight="400">
                      {!!algorithmPrice?.total ? currencyFormat(+algorithmPrice.total) : MISSING_DATA}
                    </Typography>
                  </Maybe>
                </TableCell>
                <TableCell sx={{ ...cellBorder, borderLeftColor: "rgba(0, 0, 0, 0.12)" }}>
                  <Maybe condition={algorithmPriceWithRisk === undefined && !isMissingEstimate}>
                    <LinearProgress sx={{ maxWidth: 50 }} />
                  </Maybe>
                  <Maybe condition={algorithmPriceWithRisk !== undefined || isMissingEstimate}>
                    <Typography variant="body2" component="p" fontWeight="400">
                      {!!algorithmPriceWithRisk?.unitPrice
                        ? currencyFormat(+algorithmPriceWithRisk.unitPrice)
                        : MISSING_DATA}
                    </Typography>
                  </Maybe>
                </TableCell>
                <TableCell>
                  <Maybe condition={portfolioQuantity === undefined}>
                    <LinearProgress sx={{ maxWidth: 50 }} />
                  </Maybe>
                  <Maybe condition={portfolioQuantity !== undefined}>
                    <Typography variant="body2" component="p" fontWeight="400">
                      {numberFormat(portfolioQuantity)}
                    </Typography>
                  </Maybe>
                </TableCell>
                <TableCell>
                  <Maybe condition={algorithmPriceWithRisk === undefined && !isMissingEstimate}>
                    <LinearProgress sx={{ maxWidth: 50 }} />
                  </Maybe>
                  <Maybe condition={algorithmPriceWithRisk !== undefined || isMissingEstimate}>
                    <Typography variant="body2" component="p" fontWeight="400">
                      {!!algorithmPriceWithRisk?.total ? currencyFormat(+algorithmPriceWithRisk.total) : MISSING_DATA}
                    </Typography>
                  </Maybe>
                </TableCell>
                <TableCell sx={separator}></TableCell>
                <TableCell sx={{ ...cellBorder, borderLeftColor: "rgba(0, 0, 0, 0.12)" }}>
                  <Maybe condition={algorithmPrice === undefined && !isMissingEstimate}>
                    <LinearProgress sx={{ maxWidth: 50 }} />
                  </Maybe>
                  <Maybe condition={algorithmPrice !== undefined || isMissingEstimate}>
                    <Typography variant="body2" component="p" fontWeight="400">
                      {!!algorithmPrice?.unitCost ? currencyFormat(+algorithmPrice.unitCost, 4) : MISSING_DATA}
                    </Typography>
                  </Maybe>
                </TableCell>
                <TableCell>
                  <Maybe condition={algorithmPriceWithRisk === undefined && !isMissingEstimate}>
                    <LinearProgress sx={{ maxWidth: 50 }} />
                  </Maybe>
                  <Maybe condition={algorithmPriceWithRisk !== undefined || isMissingEstimate}>
                    <Typography variant="body2" component="h4" fontWeight="400">
                      {!!algorithmPriceWithRisk?.unitCost
                        ? currencyFormat(+algorithmPriceWithRisk.unitCost, 4)
                        : MISSING_DATA}
                    </Typography>
                  </Maybe>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>
    </Box>
  );
}
