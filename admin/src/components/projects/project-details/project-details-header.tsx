import dateFormatter from "../../../utils/formatters/dateFormatter";
import { AdminProjectResponse } from "@rubiconcarbon/shared-types";
import { useCallback } from "react";
import useSnackbarVariants from "@/utils/hooks/useEnqueueVariant";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import { Box, Link, Stack } from "@mui/material";
import DetailedItem from "@/components/ui/details/detailed-item";
import useFileDownloader from "@/utils/hooks/useFileDownloader";
import PictureAsPdfIcon from "@mui/icons-material/PictureAsPdf";
import ActionButton from "@/components/ui/action-button/action-button-enhanced";
import COLORS from "@/components/ui/theme/colors";
import BackButton from "@/components/ui/back-button/back-button";
import LaunchIcon from "@mui/icons-material/Launch";

import classes from "../styles/project-details.module.scss";

const defaultPortalURL = "https://portal.rubiconcarbon.com";

const launchIconStyle = {
  fontSize: "1.5rem",
  paddingLeft: "0.4rem",
  marginBottom: "-0.4rem",
};

const disabledBtn = {
  backgroundColor: "white",
};

const enabledBtn = {
  backgroundColor: "white",
  color: COLORS.rubiconGreen,
  "&:hover": {
    backgroundColor: "white",
    color: COLORS.rubiconGreen,
  },
};

export interface DownloadResult {
  isSuccess: boolean;
  message?: string;
  error?: any;
}

export default function ProjectDetailsHeader(props: { project: AdminProjectResponse }): JSX.Element {
  const { project } = props;

  const { download } = useFileDownloader();
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const portalURL = process.env.NEXT_PUBLIC_PORTAL_URL ?? defaultPortalURL;

  const downloadPDFHandler = useCallback(async () => {
    if (project?.id && project?.registry?.id) {
      const result: DownloadResult = await download(
        `${project?.registry?.id}`,
        `/reporting/projects/report?project_id=${project?.id}`,
      );
      if (result?.isSuccess) {
        enqueueSuccess("PDF exported successfully");
      } else {
        enqueueError("Unable to download PDF");
      }
    }
  }, [download, project?.id, project?.registry?.id, enqueueSuccess, enqueueError]);

  if (!project) return <p>No Data</p>;

  return (
    <Stack direction="column" gap={2} className={classes.container}>
      <Stack direction="row" className={classes.groupContainer}>
        <Stack>
          <DetailedItem label="Project Developer:" value={project?.projectDeveloperName} />
          <DetailedItem label="Project Location:" value={project?.country?.name} />
          <DetailedItem label="Buffer Category:" value={project?.bufferCategory?.name} />
          <Box mt={1} mb={1}>
            <Link
              component={Link}
              href={`${portalURL}/projects/${project.id}`}
              target="_blank"
              rel="noreferrer"
              sx={{
                padding: ".5rem",
                whiteSpace: "nowrap",
                color: COLORS.black,
              }}
              variant="body2"
            >
              {"Open in portal"}
              <LaunchIcon sx={launchIconStyle} titleAccess="external link opens in new tab" />
            </Link>
          </Box>
        </Stack>
        <Stack>
          <DetailedItem label="Registry Name:" value={project?.registryName} />
          <DetailedItem label="Registry Project ID:" value={project?.registryProjectId} />
          <DetailedItem label="Project Category:" value={project?.projectType?.category} />
          <Maybe condition={!!project?.registryLink}>
            <Box mt={1} mb={1}>
              <Link
                component={Link}
                href={project?.registryLink}
                target="_blank"
                rel="noreferrer"
                sx={{
                  padding: ".5rem",
                  whiteSpace: "nowrap",
                  color: COLORS.black,
                }}
                variant="body2"
              >
                {"Registry"}
                <LaunchIcon sx={launchIconStyle} titleAccess="external link opens in new tab" />
              </Link>
            </Box>
          </Maybe>
        </Stack>
        <Stack>
          <DetailedItem label="Date Started:" value={dateFormatter(project?.startDate)} />
          <DetailedItem label="Date Ended:" value={dateFormatter(project?.endDate)} />
          <DetailedItem label="Project Type:" value={project?.projectType?.type} />
        </Stack>
      </Stack>
      <Stack direction="row">
        <Box ml={1}>
          <BackButton />
        </Box>
        <Maybe condition={project?.pdfReady}>
          <Box>
            <ActionButton
              style={project?.pdfReady ? enabledBtn : disabledBtn}
              variant="text"
              isDisabled={!project?.pdfReady}
              onClickHandler={downloadPDFHandler}
            >
              <PictureAsPdfIcon
                sx={{
                  color: project?.pdfReady ? COLORS.rubiconGreen : "lightGray",
                  fontSize: "35px",
                  paddingBottom: "5px",
                }}
              />
            </ActionButton>
          </Box>
        </Maybe>
      </Stack>
    </Stack>
  );
}
