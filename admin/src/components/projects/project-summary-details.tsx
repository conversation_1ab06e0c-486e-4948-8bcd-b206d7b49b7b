import dateFormatter from "../../utils/formatters/dateFormatter";
import { AdminProjectResponse } from "@rubiconcarbon/shared-types";
import { Grid } from "@mui/material";
import ItemDetails from "../ui/details/item-details";

export default function ProjectSummaryDetails(props: { project: AdminProjectResponse }): JSX.Element {
  const { project } = props;

  if (!project) return <p>No Data</p>;

  return (
    <Grid container spacing={0}>
      <Grid item xs={12}>
        <ItemDetails label="Project Developer:" value={project?.projectDeveloperName} />
      </Grid>
      <Grid item={true} xs={12}>
        <ItemDetails label="Registry Name:" value={project?.registryName} />
      </Grid>
      <Grid item={true} xs={12}>
        <ItemDetails label="Date Started:" value={dateFormatter(project?.startDate)} />
      </Grid>
      <Grid item={true} xs={12}>
        <ItemDetails label="Date Ended:" value={dateFormatter(project?.endDate)} />
      </Grid>
      <Grid item={true} xs={12}>
        <ItemDetails label="Last Verification Date:" value={dateFormatter(project?.dateOfLatestVerification)} />
      </Grid>
    </Grid>
  );
}
