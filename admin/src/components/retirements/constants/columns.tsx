import { GenericTableColumn } from "@/components/ui/generic-table/types/generic-table-column";
import InfoButton from "@/components/ui/info-button/info-button";
import StatusChip from "@/components/ui/status-chip/StatusChip";
import { RetirementStatusToLabel } from "@/mappers/transaction-status-mapper";
import { RetirementType, RetirementStatus } from "@rubiconcarbon/shared-types";
import ProductName from "@/components/ui/product-name/product-name";
import { GenericTableFieldSizeEnum } from "@/components/ui/generic-table/constants/generic-table-field-size.enum";
import { AllTransactionType, TransactionModel } from "@/models/transaction";
import OrganizationName from "@/components/ui/organization-name/organization-name";
import { GenericTableRowModel } from "@/components/ui/generic-table/types/generic-table-row-model";

export const COLUMNS: GenericTableColumn<TransactionModel>[] = [
  {
    field: "uiKey",
    label: "Transaction Key",
    width: GenericTableFieldSizeEnum.medium,
    fixedWidth: true,
  },
  {
    field: "type",
    label: "Type",
    valueOptions: [
      {
        label: "Retirement",
        value: RetirementType.RETIREMENT,
      },
      {
        label: "Transfer",
        value: RetirementType.TRANSFER_OUTFLOW,
      },
    ],
    width: GenericTableFieldSizeEnum.small,
    fixedWidth: true,
  },
  {
    field: "organization" as any,
    label: "Organization",
    width: GenericTableFieldSizeEnum.small,
    maxWidth: GenericTableFieldSizeEnum.flexmedium,
    deriveDataValue: (row) =>
      row?.type === AllTransactionType.RETIREMENT
        ? row?.retirement?.organization?.name
        : row?.transfer?.organization?.name,
    renderDataCell: (row: GenericTableRowModel<TransactionModel>): JSX.Element => (
      <OrganizationName
        organization={
          row?.type === AllTransactionType.RETIREMENT ? row?.retirement?.organization : row?.transfer?.organization
        }
        style={{ fontSize: 14, fontWeight: 300 }}
      />
    ),
  },
  {
    field: "product",
    label: "Product",
    width: GenericTableFieldSizeEnum.large,
    maxWidth: GenericTableFieldSizeEnum.xxxlarge,
    renderDataCell: (row) => <ProductName assets={row?.orders?.map(({ asset }) => asset)} style={{ fontSize: 14 }} />,
  },
  {
    field: "isPublic" as any,
    label: "Public/Private",
    type: "select",
    width: GenericTableFieldSizeEnum.flexsmall,
    fixedWidth: true,
    valueOptions: [
      { label: "Public", value: true },
      { label: "Private", value: false },
    ],
    deriveDataValue: (row) => (!!row?.retirement?.isPublic ? "Public" : "Private"),
  },
  {
    field: "amount",
    label: "Amount of Credits",
    type: "number",
    width: GenericTableFieldSizeEnum.medium,
    fixedWidth: true,
  },
  {
    field: "status",
    label: "Status",
    type: "select",
    valueOptions: [
      {
        label: RetirementStatusToLabel[RetirementStatus.ADMIN_REVIEW],
        value: RetirementStatus.ADMIN_REVIEW,
      },
      {
        label: RetirementStatusToLabel[RetirementStatus.PORTFOLIO_MANAGER_REVIEW],
        value: RetirementStatus.PORTFOLIO_MANAGER_REVIEW,
      },
      {
        label: RetirementStatusToLabel[RetirementStatus.PROCESSING],
        value: RetirementStatus.PROCESSING,
      },
      {
        label: RetirementStatusToLabel[RetirementStatus.CANCELED],
        value: RetirementStatus.CANCELED,
      },
      {
        label: RetirementStatusToLabel[RetirementStatus.COMPLETED],
        value: RetirementStatus.COMPLETED,
      },
      {
        label: RetirementStatusToLabel[RetirementStatus.FAILED],
        value: RetirementStatus.FAILED,
      },
    ],
    width: GenericTableFieldSizeEnum.small,
    fixedWidth: true,
    renderDataCell: (row) => <StatusChip status={row?.status} />,
  },
  {
    field: "dateStarted",
    type: "date",
    label: "Create Date",
    width: GenericTableFieldSizeEnum.small,
    fixedWidth: true,
  },
  {
    field: "dateFinished",
    type: "date",
    label: "Complete Date",
    width: GenericTableFieldSizeEnum.flexsmall,
    fixedWidth: true,
  },
  {
    field: "actions" as any,
    type: "action",
    exportable: false,
    sortable: false,
    width: GenericTableFieldSizeEnum.tiny,
    fixedWidth: true,
    renderDataCell: (row): JSX.Element => <InfoButton id={row?.id} />,
  },
];
