import React, { useState, useEffect, BaseSyntheticEvent } from "react";
import { useRouter } from "next/router";
import {
  AdminOrganizationQuery,
  AdminOrganizationQueryResponse,
  AdminOrganizationResponse,
  AdminUserCreateRequest,
  OrganizationUserRole,
} from "@rubiconcarbon/shared-types";
import {
  Box,
  Grid,
  Card,
  CardActions,
  CardContent,
  TextField,
  Button,
  Autocomplete,
  FormLabel,
  FormControl,
} from "@mui/material";
import RolesGroup from "../ui/roles-group/roles-group";
import InviteConfirmationModal from "./users-dialogs/invite-confirmation-modal";
import useSnackbarVariants from "@/utils/hooks/useEnqueueVariant";
import { ResponseType } from "../ui/dialogs/result-dialog";
import { handleResponseError } from "@/utils/handle-response-error";
import useNavigation from "@/utils/hooks/useNavigation";
import { useRequest } from "@rubiconcarbon/frontend-shared";
import { SERVER_PAGINATION_LIMIT } from "@/constants/constants";

export default function UserInvite(): JSX.Element {
  const [submissionInFlight] = useState<boolean>(false);
  const [availableOrganizations, setAvailableOrganizations] = useState<AdminOrganizationResponse[]>([]);
  const router = useRouter();
  const { popFromPath } = useNavigation();

  const [selectedOrganization, setSelectedOrganization] = useState<AdminOrganizationResponse>(null);

  const [isDisableOrganizations] = useState<boolean>(false);
  const [email, setEmail] = useState<string>("");
  const [firstName, setFirstName] = useState<string>("");
  const [lastName, setLastName] = useState<string>("");
  const [selectedRole, setSelectedRole] = useState<OrganizationUserRole>();
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const [isConfirmationDialogOpen, setIsConfirmationDialogOpen] = useState<boolean>(false);
  const [userInvite, setUserInvite] = useState<AdminUserCreateRequest>();

  const { data: organizationsData, error: organizationDataError } = useRequest<
    AdminOrganizationQueryResponse,
    object,
    object,
    AdminOrganizationQuery
  >({
    url: "/admin/organizations",
    queryParams: {
      limit: SERVER_PAGINATION_LIMIT,
      isCustomerPortfolio: true,
    },
  });

  useEffect(() => {
    if (organizationsData) {
      setAvailableOrganizations(organizationsData?.data);
    }
  }, [organizationsData]);

  const organizationSelectionHandler = (
    event: React.ChangeEvent<HTMLInputElement>,
    newValue: AdminOrganizationResponse | null,
  ): void => {
    setSelectedOrganization(newValue);
  };

  const onSubmit = (event: BaseSyntheticEvent): void => {
    event.preventDefault();

    const payload: AdminUserCreateRequest = {
      email,
      organizationId: selectedOrganization.id,
      organizationUserRoles: [selectedRole],
      firstName: firstName.trim(),
      lastName: lastName.trim(),
    };
    setUserInvite(payload);
    setIsConfirmationDialogOpen(true);
  };

  const organizationDefaultProps = {
    options:
      availableOrganizations
        .filter((organization) => organization.isEnabled)
        ?.sort((a, b) => a.name?.localeCompare(b.name)) ?? [],
    getOptionLabel: (option: AdminOrganizationResponse) => (option?.name ? option.name : ""),
    isOptionEqualToValue: (option: AdminOrganizationResponse, value: AdminOrganizationResponse) =>
      option.id === value.id,
  };

  const roleChangeHandler = (event: React.ChangeEvent<HTMLInputElement>, role: OrganizationUserRole): void => {
    setSelectedRole(role);
  };

  useEffect(() => {
    handleResponseError(organizationDataError);
  }, [organizationDataError]);

  const emailHandler = (event: React.ChangeEvent<HTMLInputElement>): void => {
    setEmail(event.target.value);
  };

  function onSuccessHandler(message: string): void {
    enqueueSuccess(message);
  }

  function closeConfirmationHandler(submitResult?: { result: ResponseType }): void {
    setIsConfirmationDialogOpen(false);

    if (submitResult.result && submitResult.result === ResponseType.SUCCESS) popFromPath(1);
  }

  const itemDtailsCard = (
    <React.Fragment>
      <CardContent>
        <form id="permissions-request-form" onSubmit={onSubmit}>
          <fieldset disabled={submissionInFlight} style={{ display: "contents" }}>
            <Grid container gap={4} flexDirection="column">
              <FormControl fullWidth>
                <FormLabel component="legend" sx={{ marginTop: 1.5, marginBottom: 2.5, lineHeight: "150%" }}>
                  Create a new user in an organization. We&apos;ll send them an email to setup their password.
                </FormLabel>
                <Autocomplete
                  {...organizationDefaultProps}
                  onChange={organizationSelectionHandler}
                  value={selectedOrganization}
                  loading
                  disabled={isDisableOrganizations}
                  disablePortal
                  id="organization"
                  renderInput={(params) => <TextField required {...params} label="Organization" />}
                />
                <TextField
                  type={"email"}
                  sx={{ marginTop: "20px" }}
                  id="email"
                  label="Email Address"
                  value={email}
                  onChange={emailHandler}
                  required
                  inputProps={{ maxLength: 256 }}
                />
                <TextField
                  sx={{ marginTop: "20px" }}
                  type={"text"}
                  id="firstName"
                  label="First Name"
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value.trimStart())}
                  required
                  inputProps={{ maxLength: 256 }}
                />
                <TextField
                  sx={{ marginTop: "20px" }}
                  type={"text"}
                  id="lastName"
                  label="Last Name"
                  value={lastName}
                  onChange={(e) => setLastName(e.target.value.trimStart())}
                  required
                  inputProps={{ maxLength: 256 }}
                />
              </FormControl>
              <FormControl fullWidth sx={{ marginTop: -3 }}>
                <RolesGroup selectedRole={selectedRole} roleChangeHandler={roleChangeHandler} />
              </FormControl>
            </Grid>
          </fieldset>
        </form>
      </CardContent>
      <CardActions
        sx={{
          justifyContent: "space-between",
        }}
      >
        <Box ml={2} sx={{ display: "flex" }}></Box>
        <Box mr={2} mb={1} mt={1}>
          <Button
            sx={{ marginRight: 3, fontWeight: 600 }}
            disabled={submissionInFlight}
            variant="text"
            onClick={() => router.back()}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            type="submit"
            form="permissions-request-form"
            sx={{ px: 3.5, color: "#FFFFFF" }}
            disabled={submissionInFlight}
          >
            Create
          </Button>
        </Box>
      </CardActions>
    </React.Fragment>
  );

  return (
    <Box mt={4}>
      <Grid container spacing={0} direction="column" alignItems="center" style={{ minHeight: "100vh" }}>
        <Box mt={0} sx={{ width: "100%", minWidth: 800, maxWidth: 1200 }}>
          <Card variant="elevation" sx={{ borderRadius: 4, backgroundColor: "#FAFAFA" }}>
            {itemDtailsCard}
          </Card>
        </Box>
      </Grid>
      <Box>
        {userInvite && selectedOrganization && (
          <InviteConfirmationModal
            isOpen={isConfirmationDialogOpen}
            userInvite={userInvite}
            organizationName={selectedOrganization.name}
            onClose={closeConfirmationHandler}
            onConfirm={onSuccessHandler}
            onError={enqueueError}
          />
        )}
      </Box>
    </Box>
  );
}
