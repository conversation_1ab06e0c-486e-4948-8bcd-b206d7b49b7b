import { useCallback, useContext, useEffect, useMemo, useState } from "react";
import useSnackbarVariants from "@/utils/hooks/useEnqueueVariant";
import { useLogger } from "@/providers/logging";
import {
  AdminMarketNewsQueryResponse,
  AdminMarketNewsResponse,
  MarketNewsScope,
  PermissionEnum,
} from "@rubiconcarbon/shared-types";
import { Badge, Box, Card, Stack, Tab, Tabs } from "@mui/material";
import MarketIntelligenceItem from "./market-intelligence-item";
import Maybe from "../../maybe/maybe";
import MarkeIntelSearchBar from "./market-intel-search-bar";
import useSWR from "swr";
import { MITabMessages, TabLabels } from "../constants/tabs";
import { AuthContext } from "@/providers/auth-provider";
import AddIcon from "@mui/icons-material/Add";
import ActionButton from "@/components/ui/action-button/action-button-enhanced";
import useNavigation from "@/utils/hooks/useNavigation";
import { generatePath } from "@/utils/helpers/general/fetch";
import { GenericRecord } from "@rubiconcarbon/frontend-shared";

import classes from "./styles.module.scss";

const MAX_REQ_SIZE = 500;

interface TabsDef {
  label: TabLabels;
  permission: PermissionEnum;
}

const tabTypes: TabsDef[] = [
  {
    label: TabLabels.PENDING_REVIEW, //Science Team Only
    permission: PermissionEnum.MARKET_NEWS_UPDATE,
  },
  /*{
    label: TabLabels.ALL, //Science Team Only
    permission: PermissionEnum.MARKET_NEWS_UPDATE,
  },*/
  {
    label: TabLabels.NEWS,
    permission: PermissionEnum.LOGIN,
  },
];

type MarketIntelligenceMngmtProps = {
  isExtendedFunc?: boolean;
};

export default function MarketIntelligenceMngmt({ isExtendedFunc = false }: MarketIntelligenceMngmtProps): JSX.Element {
  const { enqueueError } = useSnackbarVariants();
  const { logger } = useLogger();
  const [tabData, setTabData] = useState<AdminMarketNewsResponse[]>();
  const [pendingReviewCount, setPendingReviewCount] = useState<number>();
  const [isClearSearch, setIsClearSearch] = useState<boolean>(false);
  const [isCorporateWatchlistFiltered, setIsCorporateWatchlistFiltered] = useState<boolean>(false);
  const [currentSearchTerm, setCurrentSearchTerm] = useState<string>("");
  const { user: loginUser } = useContext(AuthContext);
  const allowAllTabs: boolean = useMemo(() => loginUser.hasPermission(PermissionEnum.MARKET_NEWS_UPDATE), [loginUser]);
  const [selectedTab, setSelectedTab] = useState<TabsDef>(tabTypes[isExtendedFunc ? (allowAllTabs ? 0 : 2) : 2]);
  const { pushOnToPath } = useNavigation();

  const queryDate = useMemo(() => {
    const d = new Date();
    d.setDate(d.getDate() - 5);
    return d.toISOString();
  }, []);

  const getQueryParams = useCallback((): GenericRecord => {
    switch (selectedTab?.label) {
      case TabLabels.PENDING_REVIEW:
        return {
          orderBy: "articleDate",
          scopes: MarketNewsScope.PENDING_REVIEW,
          startDate: queryDate,
          limit: MAX_REQ_SIZE,
        };
      case TabLabels.NEWS:
        return {
          orderBy: "articleDate",
          isIrrelevant: false,
          scopes: [MarketNewsScope.CUSTOMER_PORTAL, MarketNewsScope.ADMIN_PLATFORM].toString(),
          limit: MAX_REQ_SIZE,
        };

      default:
        break;
    }

    return {
      orderBy: "articleDate",
      startDate: queryDate,
      limit: MAX_REQ_SIZE,
    };
  }, [selectedTab?.label, queryDate]);

  const [requestUrl, setRequestUrl] = useState<string>();

  const {
    data: marketIntelligenceList,
    error,
    mutate,
  } = useSWR<AdminMarketNewsQueryResponse>(!!requestUrl ? requestUrl : null, {
    revalidateOnFocus: false,
  });

  if (!!error) {
    enqueueError(MITabMessages.MI_FETCH_DATA_FAIL);
    logger.error(`${MITabMessages.MI_FETCH_DATA_FAIL}: ${error?.message}.`, {});
  }

  useEffect(() => {
    const queryParameters = getQueryParams();
    setRequestUrl(generatePath("/admin/market-news", {}, queryParameters));
  }, [marketIntelligenceList, selectedTab, getQueryParams]);

  useEffect(() => {
    if (!!marketIntelligenceList?.data) {
      if (selectedTab?.label === TabLabels.PENDING_REVIEW) {
        setPendingReviewCount(marketIntelligenceList?.data?.length ?? 0);
      }
      setTabData(marketIntelligenceList?.data);
    }
  }, [marketIntelligenceList?.data, selectedTab?.label]);

  const TabLabel = (props: { tab: TabsDef }): JSX.Element => {
    const { tab } = props;
    return (
      <Stack direction="row" gap={1}>
        <Box>{tab.label}</Box>
        <Maybe condition={tab.label === TabLabels.PENDING_REVIEW}>
          <Box ml={1.5}>
            <Badge
              badgeContent={pendingReviewCount}
              sx={{
                "& .MuiBadge-badge": {
                  backgroundColor: "rgba(0, 0, 0, 0.04)",
                },
              }}
            />
          </Box>
        </Maybe>
      </Stack>
    );
  };

  const applyFilters = (
    searchKey: string = currentSearchTerm,
    corporateWatchlistOnly: boolean = isCorporateWatchlistFiltered,
  ): void => {
    let filteredData = marketIntelligenceList?.data || [];

    // Apply search filter
    if (searchKey.trim()) {
      const searchString = searchKey.toUpperCase();
      filteredData = filteredData.filter(
        (row) =>
          row.summary?.toUpperCase().includes(searchString) ||
          row.header?.toUpperCase().includes(searchString) ||
          row.source?.toUpperCase().includes(searchString) ||
          row.hitword?.toUpperCase().includes(searchString),
      );
    }

    // Apply corporate watchlist filter
    if (corporateWatchlistOnly) {
      filteredData = filteredData.filter((row) => row.scopes?.includes(MarketNewsScope.CORPORATE_WATCHLIST));
    }

    setTabData(filteredData);
  };

  const onSearchChangeHandler = (searchKey: string): void => {
    setCurrentSearchTerm(searchKey);
    applyFilters(searchKey, isCorporateWatchlistFiltered);
  };

  const onCorporateWatchlistFilterHandler = (isChecked: boolean): void => {
    setIsCorporateWatchlistFiltered(isChecked);
    applyFilters(currentSearchTerm, isChecked);
  };

  const onSearchClearHandler = (): void => {
    setTabData(marketIntelligenceList?.data);
    setIsClearSearch(false);
    setIsCorporateWatchlistFiltered(false);
    setCurrentSearchTerm("");
  };

  const tabChangeHandler = (newValue: TabsDef): void => {
    setSelectedTab(newValue);
    setIsClearSearch(true);
  };

  const onMarketItelItemSave = (): void => {
    mutate();
    setIsClearSearch(true);
  };
  return (
    <>
      <Maybe condition={isExtendedFunc}>
        <Box sx={{ textAlign: "right" }}>
          <ActionButton
            onClickHandler={() => pushOnToPath(`/add-market-news`)}
            requiredPermission={PermissionEnum.MARKET_NEWS_UPDATE}
            style={{ width: "140px", textTransform: "capitalize" }}
            startIcon={<AddIcon />}
          >
            Add News
          </ActionButton>
        </Box>
      </Maybe>
      <Maybe condition={isExtendedFunc}>
        <Box className={classes.tabsContainer}>
          <Tabs
            sx={{ height: "30px", minHeight: "40px" }}
            value={selectedTab}
            onChange={(_, newValue) => tabChangeHandler(newValue)}
          >
            {tabTypes.map(
              (t) =>
                loginUser.hasPermission(t.permission) && (
                  <Tab key={t.label} value={t} label={<TabLabel tab={t} />} sx={{ textTransform: "none" }} />
                ),
            )}
          </Tabs>
        </Box>
      </Maybe>
      <Box mt={2} component={Card}>
        <Stack direction="column">
          <Box sx={{ padding: "10px", backgroundColor: "rgba(238, 238, 238,1)", height: "70px" }}>
            <MarkeIntelSearchBar
              onChange={onSearchChangeHandler}
              onClear={onSearchClearHandler}
              isClear={isClearSearch}
              onCorporateWatchlistFilter={onCorporateWatchlistFilterHandler}
              isCorporateWatchlistFiltered={isCorporateWatchlistFiltered}
            />
          </Box>

          {tabData?.map((item) => (
            <MarketIntelligenceItem
              key={item.id}
              item={item}
              isExtendedFunc={isExtendedFunc}
              onSave={onMarketItelItemSave}
            />
          ))}
        </Stack>
      </Box>
    </>
  );
}
