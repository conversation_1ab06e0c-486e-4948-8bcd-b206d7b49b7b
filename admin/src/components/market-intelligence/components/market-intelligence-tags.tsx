import { AdminMarketNewsResponse, MarketNewsScope } from "@rubiconcarbon/shared-types";
import { Chip, Stack, Tooltip } from "@mui/material";
import { useMemo, useState } from "react";
import VisibilityModal from "./market-intel-visibility-modal";
import ActionButton from "@/components/ui/action-button/action-button-enhanced";
import COLORS from "@/components/ui/theme/colors";
import { SCOPES_MAPPING, COLOR_MAPPING, TagType } from "../constants/tags";

interface TagDef {
  label: string;
  color: string;
  tooltip?: string;
}
const createTagList = (item: AdminMarketNewsResponse): TagDef[] => {
  const tagList: TagDef[] = [];

  if (!!item) {
    if (item?.isIrrelevant) {
      tagList.push({
        label: SCOPES_MAPPING["irrelevant"],
        color: COLOR_MAPPING[TagType.irrelevant],
      });
    } else {
      if (item?.scopes?.includes(MarketNewsScope.CUSTOMER_PORTAL)) {
        tagList.push({
          label: SCOPES_MAPPING[MarketNewsScope.CUSTOMER_PORTAL],
          color: COLOR_MAPPING[TagType.portal],
        });
      }

      if (item?.scopes?.includes(MarketNewsScope.ADMIN_PLATFORM)) {
        tagList.push({
          label: SCOPES_MAPPING[MarketNewsScope.ADMIN_PLATFORM],
          color: COLOR_MAPPING[TagType.platform],
        });
      }

      if (item?.scopes?.includes(MarketNewsScope.CORPORATE_WATCHLIST)) {
        if (item?.hitword) {
          const termsToStrip = ["voluntary carbon market", "carbon credits", "net zero"];
          let processedHitword = item.hitword;

          const stripRegex = new RegExp(termsToStrip.join("|"), "gi");
          processedHitword = processedHitword.replace(stripRegex, "").trim();

          if (processedHitword) {
            tagList.push({
              label: processedHitword,
              color: COLOR_MAPPING[TagType.corporate],
            });
          }
        }
      }

      if (item?.scopes?.includes(MarketNewsScope.CORPORATE_WATCHLIST)) {
        tagList.push({
          label: SCOPES_MAPPING[MarketNewsScope.CORPORATE_WATCHLIST],
          color: COLOR_MAPPING[TagType.corporate],
        });
      }
    }
  }

  //Adding projects tags
  item?.projects?.forEach((p) => {
    tagList.push({
      label: p?.registryProjectId,
      color: COLOR_MAPPING[TagType.project],
      tooltip: p?.name,
    });
  });

  return tagList;
};

type MarketIntelligenceItemProps = {
  item: AdminMarketNewsResponse;
  onSave: () => void;
};

export default function MarketIntelligenceTags({ item, onSave }: MarketIntelligenceItemProps): JSX.Element {
  const [isOpen, setIsOpen] = useState<boolean>(false);

  const isReview: boolean = useMemo(
    () =>
      !item?.isIrrelevant &&
      !item?.scopes?.includes(MarketNewsScope.ADMIN_PLATFORM) &&
      !item?.scopes?.includes(MarketNewsScope.CUSTOMER_PORTAL),
    [item],
  );

  const tagList = createTagList(item);

  const displayTagList = isReview
    ? tagList.filter(
        (tag) => tag.color === COLOR_MAPPING[TagType.corporate] || tag.color === COLOR_MAPPING[TagType.project],
      )
    : tagList;

  const saveVisibilityHandler = (): void => {
    onSave();
    setIsOpen(false);
  };

  const closeVisibilityHandler = (): void => {
    setIsOpen(false);
  };

  return (
    <>
      <Stack direction="row" gap={1} alignItems="center">
        {isReview && (
          <ActionButton
            style={{
              fontWeight: 500,
              width: "90px",
              textTransform: "capitalize",
              borderColor: "rgba(0,0,0,0.23)",
              backgroundColor: "white",
              color: COLORS.red,
              "&:hover": {
                color: COLORS.red,
                backgroundColor: "rgba(245, 245 , 245,1)",
              },
            }}
            onClickHandler={() => setIsOpen(true)}
          >
            Review
          </ActionButton>
        )}
        {displayTagList.length > 0 &&
          displayTagList.map((tag) => (
            <Tooltip key={tag.label} title={!!tag?.tooltip ? tag.tooltip : ""}>
              <Chip
                size="small"
                label={tag.label}
                variant="outlined"
                sx={{
                  color: tag.color,
                  borderColor: tag.color,
                }}
              />
            </Tooltip>
          ))}
        <VisibilityModal isOpen={isOpen} item={item} onSave={saveVisibilityHandler} onClose={closeVisibilityHandler} />
      </Stack>
    </>
  );
}
