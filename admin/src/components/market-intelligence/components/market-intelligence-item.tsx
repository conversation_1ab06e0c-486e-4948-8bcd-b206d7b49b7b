import { MouseEvent, useContext } from "react";
import Link from "next/link";
//mport useSnackbarVariants from "@/utils/hooks/useEnqueueVariant";
//import { useLogger } from "@/providers/logging";
import { AdminMarketNewsResponse, PermissionEnum, uuid } from "@rubiconcarbon/shared-types";
import { Box, Chip, Stack, Tooltip, Typography } from "@mui/material";
import { LaunchRounded } from "@mui/icons-material";
import ItemSummary from "./item-summary";
import useNavigation from "@/utils/hooks/useNavigation";
import COLORS from "../../ui/theme/colors";
import CreateIcon from "@mui/icons-material/Create";
import { elapsedTime } from "@/utils/helpers/general/general";
import MarketIntelligenceTags from "./market-intelligence-tags";
import Maybe from "@/components/maybe/maybe";
import { AuthContext } from "@/providers/auth-provider";

import classes from "./styles.module.scss";

type MarketIntelligenceItemProps = {
  item: AdminMarketNewsResponse;
  isExtendedFunc?: boolean;
  onSave: () => void;
};

export default function MarketIntelligenceItem({
  item,
  onSave,
  isExtendedFunc = false,
}: MarketIntelligenceItemProps): JSX.Element {
  const { user: loginUser } = useContext(AuthContext);
  const { pushOnToPath } = useNavigation();

  const { value, format } = elapsedTime(new Date(item.articleDate));

  const editNewsHandler = (id: uuid): void => {
    pushOnToPath(`${id}/edit-market-news`);
  };

  const onTagsSave = (): void => {
    onSave();
  };

  return (
    <Box className={classes.itemContainer}>
      <Stack gap={1} width="100%">
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <Stack direction="row" gap={1}>
            {/* updated/analzed date */}
            <Box
              sx={{
                marginTop: "4px",
                height: "20px",
                paddingRight: "8px",
                borderRight: 0.5,
                borderColor: "rgba(0, 0, 0, 0.6)",
              }}
            >
              <Tooltip title="Generated">
                <Typography variant="body2" fontWeight={700} sx={{ color: "rgba(0, 0, 0, 0.6)" }}>
                  {value}
                  {format} ago
                </Typography>
              </Tooltip>
            </Box>
            {/* source */}
            <Box paddingTop={"2px"}>
              <Tooltip title={item?.hitword}>
                <Chip
                  size="small"
                  label={item.source}
                  variant="outlined"
                  sx={{
                    color: "rgba(0,0,0,1)",
                    borderColor: "rgba(0,0,0,1)",
                  }}
                />
              </Tooltip>
            </Box>
          </Stack>
        </Stack>

        <Typography className={classes.SourceTitle} variant="body1" color="text.secondary">
          {item?.header}
        </Typography>

        {/* Summary */}
        <ItemSummary summary={item?.summary} />

        <Stack mt={1} direction="row" alignItems="center" justifyContent="space-between">
          {/* tags */}
          {isExtendedFunc ? <MarketIntelligenceTags onSave={onTagsSave} item={item} /> : <p></p>}

          {/* Edit & Read Full Article */}
          <Stack gap={1.5} direction="row" alignItems="right" justifyContent="right">
            {/* edit */}
            <Maybe condition={isExtendedFunc && loginUser.hasPermission(PermissionEnum.MARKET_NEWS_TAG)}>
              <Stack
                direction="row"
                gap={0.5}
                sx={{ color: COLORS.rubiconGreen, paddingTop: "6px", cursor: "pointer" }}
                onClick={() => editNewsHandler(item.id)}
              >
                <Box>
                  <CreateIcon sx={{ height: "20px" }} />
                </Box>
                <Typography variant="body2">Edit</Typography>
              </Stack>
            </Maybe>
            {/* Read Full Article */}
            <Tooltip title="Open article in new tab">
              <Link
                className={classes.Link}
                href={item.url}
                target="_"
                onClick={(event: MouseEvent<HTMLAnchorElement>) => event.stopPropagation()}
              >
                <Stack direction="row" alignItems="center" gap={0.5}>
                  <LaunchRounded sx={{ fontSize: 16 }} />
                  <Typography variant="body2" textTransform="none">
                    Read full article
                  </Typography>
                </Stack>
              </Link>
            </Tooltip>
          </Stack>
        </Stack>
      </Stack>
    </Box>
  );
}
