import { Divider, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, ToggleButton, ToggleButtonGroup } from "@mui/material";
import { CancelRounded, SearchRounded } from "@mui/icons-material";
import { ChangeEvent, useCallback, useEffect, useState } from "react";
import { Maybe } from "@rubiconcarbon/frontend-shared";

import classes from "./market-intel-search-bar.module.scss";

const toggleButtonStyle = {
  "&.MuiButtonBase-root": {
    color: "#094436",
    backgroundColor: "white",
  },
  "&.MuiButtonBase-root:hover": {
    backgroundColor: "#D1E1CB",
  },
  "&.Mui-selected": {
    color: "white",
    backgroundColor: "#094436",
  },
  "&.Mui-selected:hover": {
    backgroundColor: "rgb(0 78 60)",
  },
};

interface MarkeIntelSearchBarProps {
  onChange: (searchKey: string) => void;
  onClear: () => void;
  isClear?: boolean;
  onCorporateWatchlistFilter?: (isSelected: boolean) => void;
  isCorporateWatchlistFiltered?: boolean;
}

const MarkeIntelSearchBar = ({
  onChange,
  onClear,
  isClear = false,
  onCorporateWatchlistFilter,
  isCorporateWatchlistFiltered = false,
}: MarkeIntelSearchBarProps): JSX.Element => {
  const [searchTerm, setSearchTerm] = useState<string>("");

  const handleSearchTermClear = useCallback((): void => {
    setSearchTerm("");
    onClear();
  }, [onClear]);

  useEffect(() => {
    if (isClear) {
      handleSearchTermClear();
    }
  }, [isClear, handleSearchTermClear]);

  const handleChange = (event: ChangeEvent<HTMLInputElement>): void => {
    event.preventDefault();
    setSearchTerm(event.target.value);
    onChange(event.target.value);
  };

  const handleCorporateWatchlistFilter = (event: React.MouseEvent<HTMLElement>, value: string | null): void => {
    if (onCorporateWatchlistFilter) {
      onCorporateWatchlistFilter(value === "watchlist");
    }
  };

  return (
    <Stack direction="row" gap={2} alignItems="center" justifyContent="space-between" width="100%">
      <Stack className={classes.SearchBar} gap={0.5} direction="row" alignItems="center" maxWidth={375}>
        <SearchRounded />
        <Divider sx={{ height: 28, m: 0.5 }} orientation="vertical" />
        <Stack direction="row" justifyContent="space-between" alignItems="center" id="project-search-bar" width="100%">
          <TextField
            variant="standard"
            placeholder="Article title, content, publisher or hitword"
            value={searchTerm}
            inputProps={{ "aria-label": "search by article, publisher or keyword" }}
            InputProps={{
              disableUnderline: true,
              endAdornment: (
                <Maybe condition={!!searchTerm}>
                  <IconButton onClick={handleSearchTermClear}>
                    <CancelRounded fontSize="medium" />
                  </IconButton>
                </Maybe>
              ),
            }}
            fullWidth
            onChange={handleChange}
          />
        </Stack>
      </Stack>
      {onCorporateWatchlistFilter && (
        <Stack direction="row" alignItems="center" gap={1}>
          <span>Filter:</span>
          <ToggleButtonGroup
            value={isCorporateWatchlistFiltered ? "watchlist" : "all"}
            exclusive
            onChange={handleCorporateWatchlistFilter}
            aria-label="corporate watchlist filter"
            size="small"
            sx={{ maxWidth: 350 }}
          >
            <ToggleButton value="all" aria-label="all articles" sx={toggleButtonStyle}>
              All Articles
            </ToggleButton>
            <ToggleButton value="watchlist" aria-label="corporate watchlist only" sx={toggleButtonStyle}>
              Corporate Watchlist
            </ToggleButton>
          </ToggleButtonGroup>
        </Stack>
      )}
    </Stack>
  );
};

export default MarkeIntelSearchBar;
