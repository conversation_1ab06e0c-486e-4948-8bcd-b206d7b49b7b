import React from "react";
import { Box } from "@mui/material";
import FileDownloadIcon from "@mui/icons-material/FileDownload";
import { CsvBuilder } from "filefy";
import ActionButton from "@components/ui/action-button/action-button-enhanced";
import Decimal from "decimal.js";
import dateFormatterEST from "@utils/formatters/est-date-formatter";
import { MISSING_DATA } from "@constants/constants";
import { PriceData } from "./historical-price-modal";

const PriceTypeMapping: Record<string, string> = {
  buyback_price: "BUYBACK PRICE",
  purchase_price_without_buffer: "PURCHASE PRICE WITHOUT BUFFER",
  purchase_price_with_buffer: "PURCHASE PRICE WITH BUFFER",
};

interface IExportFormatter {
  func: (input: string | number | Decimal | Date | boolean | Map<string, string | number | Date>) => any;
  inputFields?: string[];
}

interface ExportColDef {
  name: string;
  displayName: string;
  formatter?: IExportFormatter;
}

export interface HistPriceExportProps {
  historicalPrices: PriceData[];
  bookName: string;
}

export default function HistPriceExport(props: HistPriceExportProps): JSX.Element {
  const { historicalPrices, bookName } = props;

  const exportCols: ExportColDef[] = [
    {
      name: "name",
      displayName: "Portfolio Name",
    },
    {
      name: "new_price",
      displayName: "Price",
      formatter: {
        func: (input: string | number | Decimal | Date | boolean | Map<string, string | number | Date>) => {
          if (input instanceof Decimal) {
            return !!input ? "$" + input.toFixed(2, Decimal.ROUND_HALF_UP).toString() : MISSING_DATA;
          }
          return MISSING_DATA;
        },
      },
    },
    {
      name: "timestamp",
      displayName: "Date",
      formatter: {
        func: (input: string | number | Decimal | Date | boolean | Map<string, string | number | Date>) => {
          if (typeof input === 'string') {
            return dateFormatterEST(input);
          }
          return MISSING_DATA;
        }
      },
    },
    {
      name: "type",
      displayName: "Type",
      formatter: {
        func: (input: string | number | Decimal | Date | boolean | Map<string, string | number | Date>) => {
          if (typeof input === 'string') {
            return !!PriceTypeMapping[input] ? PriceTypeMapping[input] : input;
          }
          return String(input);
        },
      },
    },
  ];

  const getFormatterInputFields = (
    inputFields: string[],
    row: PriceData,
    colDef: ExportColDef,
  ): Map<string, string | number | Date> | string => {
    if (!inputFields) return (row as any)[colDef.name];

    const map = new Map<string, string | number | Date>();
    inputFields.forEach((element) => {
      map.set(element, (row as any)[element]);
    });

    return map;
  };

  const formatRowValue = (row: PriceData, exportColDef: ExportColDef): string => {
    if ((row as any)[exportColDef.name] === null) return MISSING_DATA;

    if (!!exportColDef?.formatter) {
      let formatterInputFields = null;
      if (!!exportColDef?.formatter?.inputFields) {
        formatterInputFields = getFormatterInputFields(exportColDef.formatter.inputFields, row, exportColDef);
      } else {
        formatterInputFields = (row as any)[exportColDef.name];
      }
      return exportColDef.formatter.func(formatterInputFields);
    }
    return (row as any)[exportColDef.name];
  };

  const exportHandler = (): void => {
    if (!!historicalPrices) {
      const columns = exportCols?.map((colDef) => colDef.displayName);
      const rows = historicalPrices
        .map((row) => {
          return { ...row, name: bookName };
        })
        .map((row) => exportCols.map((colDef) => formatRowValue(row, colDef)));
      const csvBuilder = new CsvBuilder("historical_prices.csv");
      csvBuilder.setColumns(columns).addRows(rows).exportFile();
    }
  };

  return (
    <Box>
      <ActionButton
        onClickHandler={exportHandler}
        startIcon={<FileDownloadIcon />}
        tooltip="Export table data to csv file"
        style={{
          borderColor: "rgba(0, 0, 0, 0.23)",
          color: "rgba(0, 0, 0, 0.54)",
          backgroundColor: "rgba(255, 255, 255, 1)",
          ontWeight: 600,
          "&:hover": {
            backgroundColor: "rgba(255, 255, 255, 1)",
            boxShadow: "rgb(0 0 0 / 10%) 0px 4px 4px",
          },
        }}
      >
        Export
      </ActionButton>
    </Box>
  );
}
